"""
Main FastAPI application for the Enhanced RAG System.
"""
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
import time
import structlog

from app.config import settings
from app.database import db_manager
from app.api import chat, chatbots, actions, email_templates, widgets

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting Enhanced RAG System API")
    try:
        await db_manager.initialize()
        logger.info("Database connections initialized")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Enhanced RAG System API")
    try:
        await db_manager.close()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title=settings.api_title,
    version=settings.api_version,
    description=settings.api_description,
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add trusted host middleware for production
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # Configure properly for production
    )


# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all requests."""
    start_time = time.time()
    
    # Extract basic request info
    client_ip = request.client.host if request.client else "unknown"
    method = request.method
    url = str(request.url)
    
    logger.info(
        "Request started",
        method=method,
        url=url,
        client_ip=client_ip
    )
    
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        
        logger.info(
            "Request completed",
            method=method,
            url=url,
            status_code=response.status_code,
            process_time=round(process_time, 4)
        )
        
        # Add process time header
        response.headers["X-Process-Time"] = str(process_time)
        
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(
            "Request failed",
            method=method,
            url=url,
            error=str(e),
            process_time=round(process_time, 4)
        )
        raise


# Validation error handler
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle Pydantic validation errors."""
    logger.warning(
        "Validation error",
        errors=exc.errors(),
        url=str(request.url)
    )

    # Format validation errors for better frontend handling
    formatted_errors = []
    for error in exc.errors():
        location = ".".join(str(loc) for loc in error["loc"])
        formatted_errors.append(f"{location}: {error['msg']}")

    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "Validation error",
            "detail": formatted_errors if len(formatted_errors) > 1 else formatted_errors[0] if formatted_errors else "Invalid input",
            "error_code": 422,
            "errors": exc.errors()  # Include raw errors for debugging
        }
    )


# Global exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions."""
    logger.warning(
        "HTTP exception",
        status_code=exc.status_code,
        detail=exc.detail,
        url=str(request.url)
    )

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": exc.status_code
        }
    )


# Pydantic validation error handler
@app.exception_handler(ValidationError)
async def pydantic_validation_exception_handler(request: Request, exc: ValidationError):
    """Handle Pydantic validation errors."""
    logger.warning(
        "Pydantic validation error",
        errors=exc.errors(),
        url=str(request.url)
    )

    # Format validation errors for better frontend handling
    formatted_errors = []
    for error in exc.errors():
        location = ".".join(str(loc) for loc in error["loc"])
        formatted_errors.append(f"{location}: {error['msg']}")

    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "Validation error",
            "detail": formatted_errors if len(formatted_errors) > 1 else formatted_errors[0] if formatted_errors else "Invalid input",
            "error_code": 422,
            "errors": exc.errors()  # Include raw errors for debugging
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(
        "Unhandled exception",
        error=str(exc),
        url=str(request.url),
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "error_code": 500
        }
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Test database connection
        supabase_client = db_manager.get_supabase_client()
        test_query = supabase_client.table("chatbots").select("id").limit(1).execute()
        
        return {
            "status": "healthy",
            "version": settings.api_version,
            "database": "connected",
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "version": settings.api_version,
                "database": "disconnected",
                "error": str(e),
                "timestamp": time.time()
            }
        )


# API info endpoint
@app.get("/")
async def api_info():
    """API information endpoint."""
    return {
        "name": settings.api_title,
        "version": settings.api_version,
        "description": settings.api_description,
        "docs_url": "/docs" if settings.debug else "Documentation disabled in production",
        "health_url": "/health",
        "features": [
            "Natural conversation handling",
            "Function calling capabilities",
            "Email response formatting", 
            "Embeddable chat widgets",
            "RAG-based question answering",
            "Multi-tenant chatbot management"
        ]
    }


# Include API routers
app.include_router(chat.router, prefix="/api/v1")
app.include_router(chatbots.router, prefix="/api/v1")
app.include_router(actions.router, prefix="/api/v1")
app.include_router(email_templates.router, prefix="/api/v1")
app.include_router(widgets.router, prefix="/api/v1")
app.include_router(widgets.public_router, prefix="/api/v1")


# Widget iframe endpoint
@app.get("/widget/{api_key}")
async def get_widget_interface(api_key: str):
    """Serve the widget interface HTML."""
    from app.services.widget_service import get_widget_service

    try:
        widget_service = await get_widget_service()
        widget = await widget_service.get_widget_by_api_key(api_key)

        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")

        # Generate the widget HTML interface
        widget_html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Widget</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #f8f9fa;
        }}

        .chat-header {{
            background: #007bff;
            color: white;
            padding: 16px;
            text-align: center;
            font-weight: 600;
        }}

        .chat-messages {{
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }}

        .message {{
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }}

        .message.user {{
            background: #007bff;
            color: white;
            align-self: flex-end;
            margin-left: auto;
        }}

        .message.bot {{
            background: white;
            color: #333;
            align-self: flex-start;
            border: 1px solid #e9ecef;
        }}

        .chat-input {{
            padding: 16px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 8px;
        }}

        .chat-input input {{
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 24px;
            outline: none;
            font-size: 14px;
        }}

        .chat-input input:focus {{
            border-color: #007bff;
        }}

        .chat-input button {{
            padding: 12px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 24px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }}

        .chat-input button:hover {{
            background: #0056b3;
        }}

        .chat-input button:disabled {{
            background: #6c757d;
            cursor: not-allowed;
        }}

        .welcome-message {{
            text-align: center;
            color: #6c757d;
            font-style: italic;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <div class="chat-header">
        AI Assistant
    </div>

    <div class="chat-messages" id="messages">
        <div class="welcome-message">
            Hello! How can I help you today?
        </div>
    </div>

    <div class="chat-input">
        <input type="text" id="messageInput" placeholder="Type your message..." />
        <button id="sendButton">Send</button>
    </div>

    <script>
        const apiKey = '{api_key}';
        const messagesContainer = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        function addMessage(content, isUser = false) {{
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${{isUser ? 'user' : 'bot'}}`;
            messageDiv.textContent = content;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }}

        async function sendMessage() {{
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage(message, true);
            messageInput.value = '';
            sendButton.disabled = true;

            try {{
                const response = await fetch('/api/v1/chat/widget/' + apiKey, {{
                    method: 'POST',
                    headers: {{
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    }},
                    body: JSON.stringify({{
                        message: message,
                        user_id: 'widget_user_' + Date.now(),
                        session_id: 'widget_session_' + Date.now()
                    }})
                }});

                if (response.ok) {{
                    const data = await response.json();
                    addMessage(data.response || 'Sorry, I could not process your request.');
                }} else {{
                    addMessage('Sorry, there was an error processing your request.');
                }}
            }} catch (error) {{
                console.error('Error:', error);
                addMessage('Sorry, there was a connection error.');
            }} finally {{
                sendButton.disabled = false;
                messageInput.focus();
            }}
        }}

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', function(e) {{
            if (e.key === 'Enter') {{
                sendMessage();
            }}
        }});

        // Focus input on load
        messageInput.focus();
    </script>
</body>
</html>
"""

        from fastapi.responses import HTMLResponse
        return HTMLResponse(content=widget_html)

    except Exception as e:
        logger.error(f"Error serving widget interface: {e}")
        raise HTTPException(status_code=500, detail="Error loading widget")


# Widget JavaScript endpoint
@app.get("/widget.js")
async def get_widget_script():
    """Serve the legacy widget JavaScript file."""
    # This would serve the actual widget JavaScript file
    # For now, return a placeholder
    widget_js = """
(function() {
    'use strict';

    window.AIChatWidget = {
        init: function(config) {
            console.log('AI Chat Widget initialized with config:', config);

            // Create widget HTML
            const container = document.getElementById(config.containerId);
            if (!container) {
                console.error('Widget container not found:', config.containerId);
                return;
            }

            // Basic widget HTML structure
            container.innerHTML = `
                <div id="ai-chat-widget" style="
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    width: 350px;
                    height: 500px;
                    border: 1px solid #ccc;
                    border-radius: 8px;
                    background: white;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 10000;
                    font-family: Arial, sans-serif;
                ">
                    <div style="
                        background: #007bff;
                        color: white;
                        padding: 12px;
                        border-radius: 8px 8px 0 0;
                        font-weight: bold;
                    ">
                        AI Assistant
                    </div>
                    <div id="chat-messages" style="
                        height: 400px;
                        overflow-y: auto;
                        padding: 12px;
                        border-bottom: 1px solid #eee;
                    ">
                        <div style="color: #666; font-style: italic;">
                            Hello! How can I help you today?
                        </div>
                    </div>
                    <div style="padding: 12px;">
                        <input type="text" id="chat-input" placeholder="Type your message..." style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        ">
                    </div>
                </div>
            `;

            // Add event listener for message sending
            const input = container.querySelector('#chat-input');
            const messages = container.querySelector('#chat-messages');

            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && input.value.trim()) {
                    const message = input.value.trim();

                    // Add user message
                    messages.innerHTML += `
                        <div style="margin: 8px 0; text-align: right;">
                            <span style="background: #007bff; color: white; padding: 8px 12px; border-radius: 12px; display: inline-block;">
                                ${message}
                            </span>
                        </div>
                    `;

                    input.value = '';
                    messages.scrollTop = messages.scrollHeight;

                    // Send to API
                    fetch('/api/v1/chat/widget/' + config.apiKey, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Origin': window.location.origin
                        },
                        body: JSON.stringify({
                            message: message,
                            user_id: 'widget_user_' + Date.now(),
                            session_id: 'widget_session_' + Date.now()
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Add bot response
                        messages.innerHTML += `
                            <div style="margin: 8px 0;">
                                <span style="background: #f8f9fa; border: 1px solid #dee2e6; padding: 8px 12px; border-radius: 12px; display: inline-block;">
                                    ${data.response || 'Sorry, I could not process your request.'}
                                </span>
                            </div>
                        `;
                        messages.scrollTop = messages.scrollHeight;
                    })
                    .catch(error => {
                        console.error('Chat error:', error);
                        messages.innerHTML += `
                            <div style="margin: 8px 0;">
                                <span style="background: #f8d7da; color: #721c24; padding: 8px 12px; border-radius: 12px; display: inline-block;">
                                    Sorry, there was an error processing your message.
                                </span>
                            </div>
                        `;
                        messages.scrollTop = messages.scrollHeight;
                    });
                }
            });
        }
    };
})();
"""

    from fastapi.responses import Response
    return Response(
        content=widget_js,
        media_type="application/javascript",
        headers={"Cache-Control": "public, max-age=3600"}
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )
